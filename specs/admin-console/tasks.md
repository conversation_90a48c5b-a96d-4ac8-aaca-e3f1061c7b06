# 实施计划

## 任务概览

本项目将分为以下几个主要阶段，每个任务预计需要约 20 分钟完成：

## 阶段 1: 基础架构搭建

- [ ] 1. 创建 console 包结构和基础文件
  - 在 internal/admin/ 下创建 console 子包
  - 创建基础的 handlers.go、api.go 文件
  - 设置包的基本结构和导入
  - _需求: 需求 7 - 界面设计和扩展性_

- [ ] 2. 设计和实现静态资源嵌入机制
  - 创建 static 目录结构（css、js）
  - 创建 templates 目录结构
  - 实现 embed.go 文件，使用 Go embed 嵌入静态资源
  - _需求: 需求 7 - 界面设计和扩展性_

- [ ] 3. 创建基础 HTML 模板
  - 实现 base.html 基础模板，包含导航和布局
  - 创建响应式 CSS 样式文件
  - 实现基础的 JavaScript 功能
  - _需求: 需求 1 - Admin Console 主页面, 需求 7 - 界面设计和扩展性_

## 阶段 2: 核心功能实现

- [ ] 4. 实现主页面功能
  - 创建 index.html 模板
  - 实现主页面 handler
  - 显示系统基本信息（版本、启动时间等）
  - 实现导航菜单功能
  - _需求: 需求 1 - Admin Console 主页面_

- [ ] 5. 实现系统信息 API
  - 创建 SystemInfo 数据结构
  - 实现 /api/info 端点
  - 获取 Envoy Gateway 版本、启动时间、运行时信息
  - _需求: 需求 1 - Admin Console 主页面_

- [ ] 6. 集成现有 pprof 功能
  - 创建 pprof.html 模板
  - 实现 pprof 页面 handler
  - 提供到现有 pprof 端点的链接
  - 处理 pprof 未启用的情况
  - _需求: 需求 2 - pprof 性能分析功能_

## 阶段 3: 状态和资源功能

- [ ] 7. 实现服务器信息功能
  - 创建 ServerInfo 数据结构
  - 实现 /api/server_info 端点
  - 创建 server_info.html 模板
  - 显示系统各组件运行状态和基本信息
  - _需求: 需求 4 - Server Info 服务器信息功能_

- [ ] 8. 实现配置转储功能
  - 创建 ConfigDumpInfo 数据结构
  - 实现 /api/config_dump 端点
  - 集成 watchable 机制获取 provider resource 信息
  - 实现 Load 方法获取配置数据
  - 创建 config_dump.html 模板
  - _需求: 需求 3 - Config Dump 配置转储功能_

- [ ] 9. 实现统计信息功能
  - 创建 stats.html 模板
  - 实现统计页面 handler
  - 提供到 metrics server 的直接链接
  - 显示关键性能指标概览
  - _需求: 需求 5 - Stats 统计信息功能_

## 阶段 4: 集成和优化

- [ ] 10. 修改现有 admin server 集成 console
  - 修改 internal/admin/server.go
  - 添加 console 路由注册
  - 集成 watchable 机制到现有 admin runner
  - 添加 providerResources 字段到 Runner 结构
  - 实现 Load 方法获取配置数据
  - 确保与现有功能的兼容性
  - _需求: 需求 6 - API 兼容性_

- [ ] 11. 添加配置选项支持
  - 扩展 EnvoyGatewayAdmin 配置
  - 添加启用/禁用 console 的选项
  - 实现配置验证和默认值
  - _需求: 需求 6 - API 兼容性_

- [ ] 12. 实现错误处理和用户体验优化
  - 添加错误页面模板
  - 实现加载状态提示
  - 添加适当的 HTTP 安全头
  - 优化页面加载性能
  - _需求: 需求 7 - 界面设计和扩展性_

## 阶段 5: 测试和文档

- [ ] 13. 编写单元测试
  - 为所有 handler 编写单元测试
  - 为 API 端点编写测试
  - 测试模板渲染功能
  - _需求: 所有需求的质量保证_

- [ ] 14. 编写集成测试
  - 测试与现有 admin server 的集成
  - 测试与 metrics server 的集成
  - 测试 Kubernetes 资源获取功能
  - _需求: 所有需求的质量保证_

- [ ] 15. 添加 E2E 测试
  - 创建端到端测试用例
  - 测试完整的用户流程
  - 验证所有功能页面的可访问性
  - _需求: 所有需求的质量保证_

## 阶段 6: 完善和发布

- [ ] 16. 更新文档和示例
  - 更新 API 文档
  - 添加使用示例
  - 更新配置说明
  - _需求: 需求 6 - API 兼容性_

- [ ] 17. 性能优化和安全加固
  - 优化静态资源加载
  - 添加 CSRF 防护
  - 实现适当的缓存策略
  - _需求: 需求 7 - 界面设计和扩展性_

- [ ] 18. 实现 egctl dashboard 功能
  - 在 internal/cmd/egctl 中添加 dashboard 命令
  - 实现 `egctl x dashboard envoy-gateway` 命令
  - 支持端口转发到 admin server
  - 自动打开浏览器访问 console
  - _需求: 新增 egctl 集成需求_

- [ ] 19. 最终验收和部署准备
  - 验证所有需求的实现
  - 进行代码审查
  - 准备发布说明
  - _需求: 所有需求的最终验收_

## 注意事项

1. 每个任务完成后需要运行 `make test` 和 `make lint` 确保代码质量
2. 关键功能实现后需要添加相应的测试用例
3. 保持与现有代码风格和架构的一致性
4. 确保向后兼容性，不影响现有功能
