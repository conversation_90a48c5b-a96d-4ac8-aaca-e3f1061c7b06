# 技术方案设计

## 概述

为 Envoy Gateway Admin Server 添加 Web Console 功能，提供类似 Envoy Proxy Admin Web UI 的管理界面。

## 技术架构

### 整体架构

```mermaid
graph TB
    A[用户浏览器] --> B[Admin Server]
    B --> C[Static Files Handler]
    B --> D[API Handlers]
    B --> E[Template Engine]
    
    C --> F[HTML/CSS/JS 静态文件]
    D --> G[pprof Handler]
    D --> H[Status API]
    D --> I[Resources API]
    D --> J[Stats API]
    
    H --> K[Gateway Status]
    I --> L[K8s Resources]
    J --> M[Metrics Server]
    
    subgraph "现有组件"
        N[Metrics Server]
        O[pprof Endpoints]
    end
    
    M --> N
    G --> O
```

### 技术栈

- **后端**: Go (基于现有 admin server)
- **模板引擎**: Go html/template
- **前端**: HTML5 + CSS3 + Vanilla JavaScript
- **样式框架**: 轻量级 CSS 框架或自定义样式
- **数据格式**: JSON API + HTML 模板渲染

## 详细设计

### 1. 目录结构

```
internal/admin/
├── server.go              # 现有 admin server
├── console/               # 新增 console 功能
│   ├── handlers.go        # Web UI handlers
│   ├── api.go            # API handlers
│   └── templates/        # HTML 模板
│       ├── base.html     # 基础模板
│       ├── index.html    # 主页
│       ├── pprof.html    # pprof 页面
│       ├── status.html   # 状态页面
│       ├── resources.html # 资源页面
│       └── stats.html    # 统计页面
├── static/               # 静态资源
│   ├── css/
│   │   └── admin.css     # 样式文件
│   └── js/
│       └── admin.js      # JavaScript 文件
└── embed.go              # 嵌入静态资源
```

### 2. API 设计

#### 2.1 Web UI 端点
- `GET /` - Console 主页
- `GET /pprof` - pprof 功能页面
- `GET /status` - 状态页面
- `GET /resources` - 资源页面
- `GET /stats` - 统计页面

#### 2.2 API 端点
- `GET /api/status` - 获取系统状态 JSON
- `GET /api/resources` - 获取资源信息 JSON
- `GET /api/info` - 获取基本信息 JSON

#### 2.3 现有端点保持不变
- `GET /debug/pprof/*` - pprof 端点
- 重定向到 metrics server 的 `/metrics` 端点

### 3. 数据模型

#### 3.1 系统信息
```go
type SystemInfo struct {
    Version     string    `json:"version"`
    StartTime   time.Time `json:"startTime"`
    Uptime      string    `json:"uptime"`
    GoVersion   string    `json:"goVersion"`
    Platform    string    `json:"platform"`
}
```

#### 3.2 状态信息
```go
type StatusInfo struct {
    Overall     string              `json:"overall"`
    Components  []ComponentStatus   `json:"components"`
    LastUpdated time.Time          `json:"lastUpdated"`
}

type ComponentStatus struct {
    Name    string `json:"name"`
    Status  string `json:"status"`
    Message string `json:"message"`
}
```

#### 3.3 资源信息
```go
type ResourceInfo struct {
    Gateways     []ResourceSummary `json:"gateways"`
    HTTPRoutes   []ResourceSummary `json:"httpRoutes"`
    GatewayClass []ResourceSummary `json:"gatewayClass"`
}

type ResourceSummary struct {
    Name      string `json:"name"`
    Namespace string `json:"namespace"`
    Status    string `json:"status"`
    Message   string `json:"message"`
}
```

### 4. 实现策略

#### 4.1 静态资源嵌入
使用 Go embed 将静态文件嵌入到二进制文件中，避免外部依赖。

#### 4.2 模板渲染
使用 Go 标准库的 html/template 进行服务端渲染，提供基础的动态内容。

#### 4.3 API 集成
- 复用现有的 pprof 功能
- 集成 metrics server 的端点
- 通过 Kubernetes client 获取资源状态
- 提供系统运行时信息

#### 4.4 响应式设计
使用 CSS Grid/Flexbox 实现响应式布局，支持桌面和移动设备。

## 安全性考虑

1. **访问控制**: 继承现有 admin server 的安全配置
2. **输入验证**: 对所有用户输入进行验证和转义
3. **CSRF 防护**: 对状态修改操作添加 CSRF token
4. **内容安全**: 设置适当的 HTTP 安全头

## 性能考虑

1. **静态资源缓存**: 设置适当的缓存头
2. **API 响应优化**: 使用分页和过滤减少数据传输
3. **模板缓存**: 缓存编译后的模板
4. **异步加载**: 使用 JavaScript 异步加载非关键数据

## 测试策略

1. **单元测试**: 测试所有 handler 和 API 函数
2. **集成测试**: 测试与现有组件的集成
3. **E2E 测试**: 测试完整的用户流程
4. **性能测试**: 测试在高负载下的表现

## 部署和配置

1. **配置选项**: 通过现有的 EnvoyGatewayAdmin 配置启用/禁用 console
2. **向后兼容**: 确保不影响现有的 admin server 功能
3. **文档更新**: 更新相关文档和示例

## 扩展性设计

1. **插件架构**: 设计可扩展的页面和功能注册机制
2. **主题支持**: 支持自定义 CSS 主题
3. **国际化**: 预留多语言支持的接口
4. **API 版本**: 设计 API 版本管理机制
