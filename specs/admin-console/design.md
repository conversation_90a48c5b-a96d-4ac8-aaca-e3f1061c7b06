# 技术方案设计

## 概述

为 Envoy Gateway Admin Server 添加 Web Console 功能，提供类似 Envoy Proxy Admin Web UI 的管理界面。

## 技术架构

### 整体架构

```mermaid
graph TB
    A[用户浏览器] --> B[Admin Server]
    B --> C[Static Files Handler]
    B --> D[API Handlers]
    B --> E[Template Engine]
    
    C --> F[HTML/CSS/JS 静态文件]
    D --> G[pprof Handler]
    D --> H[Status API]
    D --> I[Resources API]
    D --> J[Stats API]
    
    H --> K[Gateway Status]
    I --> L[K8s Resources]
    J --> M[Metrics Server]
    
    subgraph "现有组件"
        N[Metrics Server]
        O[pprof Endpoints]
    end
    
    M --> N
    G --> O
```

### 技术栈

- **后端**: Go (基于现有 admin server)
- **模板引擎**: Go html/template
- **前端**: HTML5 + CSS3 + Vanilla JavaScript
- **样式框架**: 轻量级 CSS 框架或自定义样式
- **数据格式**: JSON API + HTML 模板渲染

## 详细设计

### 1. 目录结构

```
internal/admin/
├── server.go              # 现有 admin server
├── console/               # 新增 console 功能
│   ├── handlers.go        # Web UI handlers
│   ├── api.go            # API handlers
│   └── templates/        # HTML 模板
│       ├── base.html     # 基础模板
│       ├── index.html    # 主页
│       ├── pprof.html    # pprof 页面
│       ├── server_info.html # 服务器信息页面
│       ├── config_dump.html # 配置转储页面
│       └── stats.html    # 统计页面
├── static/               # 静态资源
│   ├── css/
│   │   └── admin.css     # 样式文件
│   └── js/
│       └── admin.js      # JavaScript 文件
└── embed.go              # 嵌入静态资源
```

### 2. API 设计

#### 2.1 Web UI 端点
- `GET /` - Console 主页
- `GET /pprof` - pprof 功能页面
- `GET /server_info` - 服务器信息页面
- `GET /config_dump` - 配置转储页面
- `GET /stats` - 统计页面

#### 2.2 API 端点
- `GET /api/server_info` - 获取服务器信息 JSON
- `GET /api/config_dump` - 获取配置转储信息 JSON
- `GET /api/info` - 获取基本信息 JSON

#### 2.3 现有端点保持不变
- `GET /debug/pprof/*` - pprof 端点
- 重定向到 metrics server 的 `/metrics` 端点

### 3. 数据模型

#### 3.1 系统信息
```go
type SystemInfo struct {
    Version     string    `json:"version"`
    StartTime   time.Time `json:"startTime"`
    Uptime      string    `json:"uptime"`
    GoVersion   string    `json:"goVersion"`
    Platform    string    `json:"platform"`
}
```

#### 3.2 服务器信息
```go
type ServerInfo struct {
    State       string              `json:"state"`
    Version     string              `json:"version"`
    Uptime      string              `json:"uptime"`
    Components  []ComponentStatus   `json:"components"`
    LastUpdated time.Time          `json:"lastUpdated"`
}

type ComponentStatus struct {
    Name    string `json:"name"`
    Status  string `json:"status"`
    Message string `json:"message"`
}
```

#### 3.3 配置转储信息
```go
type ConfigDumpInfo struct {
    Gateways     []ResourceSummary `json:"gateways"`
    HTTPRoutes   []ResourceSummary `json:"httpRoutes"`
    GatewayClass []ResourceSummary `json:"gatewayClass"`
    XdsIR        interface{}       `json:"xdsIR,omitempty"`
    InfraIR      interface{}       `json:"infraIR,omitempty"`
}

type ResourceSummary struct {
    Name      string `json:"name"`
    Namespace string `json:"namespace"`
    Status    string `json:"status"`
    Message   string `json:"message"`
}
```

### 4. 实现策略

#### 4.1 静态资源嵌入
使用 Go embed 将静态文件嵌入到二进制文件中，避免外部依赖。

#### 4.2 模板渲染
使用 Go 标准库的 html/template 进行服务端渲染，提供基础的动态内容。

#### 4.3 API 集成
- 复用现有的 pprof 功能
- 集成 metrics server 的端点
- 通过 watchable 机制读取 provider resource 信息，实现 Load 方法获取配置转储
- 作为 runner 运行，无需发布/订阅机制
- 提供系统运行时信息

#### 4.4 响应式设计
使用 CSS Grid/Flexbox 实现响应式布局，支持桌面和移动设备。

## 安全性考虑

1. **访问控制**: 继承现有 admin server 的安全配置
2. **输入验证**: 对所有用户输入进行验证和转义
3. **CSRF 防护**: 对状态修改操作添加 CSRF token
4. **内容安全**: 设置适当的 HTTP 安全头

## 性能考虑

1. **静态资源缓存**: 设置适当的缓存头
2. **API 响应优化**: 使用分页和过滤减少数据传输
3. **模板缓存**: 缓存编译后的模板
4. **异步加载**: 使用 JavaScript 异步加载非关键数据

## 测试策略

1. **单元测试**: 测试所有 handler 和 API 函数
2. **集成测试**: 测试与现有组件的集成
3. **E2E 测试**: 测试完整的用户流程
4. **性能测试**: 测试在高负载下的表现

## 部署和配置

1. **配置选项**: 通过现有的 EnvoyGatewayAdmin 配置启用/禁用 console
2. **向后兼容**: 确保不影响现有的 admin server 功能
3. **文档更新**: 更新相关文档和示例

## Console 集成设计

Console 将集成到现有的 admin server runner 中，扩展其功能：

```go
type Runner struct {
    cfg              *config.Server
    server           *http.Server
    providerResources watchable.Map[string, *message.ProviderResources] // 新增
}
```

### 集成特点

1. **扩展现有 Runner**: 在现有 admin.Runner 基础上添加 console 功能
2. **Watchable 集成**: 通过 watchable.Map 访问 provider resources
3. **按需加载**: 使用 Load 方法获取最新的配置数据
4. **统一服务**: 与现有 pprof 功能共享同一个 HTTP server

## egctl Dashboard 集成

新增 `egctl x dashboard envoy-gateway` 命令：

```bash
# 启动 Envoy Gateway console
egctl x dashboard envoy-gateway

# 指定命名空间
egctl x dashboard envoy-gateway -n envoy-gateway-system

# 指定端口
egctl x dashboard envoy-gateway --port 8080
```

### 实现特点

1. **端口转发**: 自动设置到 admin server 的端口转发
2. **浏览器集成**: 自动打开默认浏览器
3. **优雅退出**: 支持 Ctrl+C 优雅关闭

## 扩展性设计

1. **插件架构**: 设计可扩展的页面和功能注册机制
2. **主题支持**: 支持自定义 CSS 主题
3. **国际化**: 预留多语言支持的接口
4. **API 版本**: 设计 API 版本管理机制
