# 需求文档

## 介绍

为 Envoy Gateway Admin Server 添加 Web Console 界面，类似于 Envoy Proxy 的 Admin Web UI，提供可视化的管理和监控功能。该功能将帮助用户更方便地查看和管理 Envoy Gateway 的状态、配置、性能指标等信息。

## 需求

### 需求 1 - Admin Console 主页面

**用户故事：** 作为 Envoy Gateway 管理员，我希望能够通过 Web 界面访问 Admin Server，查看系统概览和导航到各个功能模块。

#### 验收标准

1. When 用户访问 Admin Server 根路径时，系统应当显示一个美观的 Console 主页面。
2. When 用户在主页面时，系统应当提供清晰的导航菜单，包含 pprof、config_dump、server_info、stats 等功能模块。
3. When 用户点击导航菜单项时，系统应当正确跳转到对应的功能页面。
4. When 用户访问主页面时，系统应当显示 Envoy Gateway 的基本信息（版本、启动时间等）。

### 需求 2 - pprof 性能分析功能

**用户故事：** 作为开发者，我希望能够通过 Web 界面访问 pprof 性能分析工具，用于调试和性能优化。

#### 验收标准

1. When 用户点击 pprof 菜单时，系统应当显示 pprof 功能页面。
2. When 用户在 pprof 页面时，系统应当提供各种 pprof 端点的链接（profile、heap、goroutine 等）。
3. When 用户点击具体的 pprof 链接时，系统应当正确跳转到对应的 pprof 端点。
4. When pprof 功能未启用时，系统应当显示相应的提示信息。

### 需求 3 - Config Dump 配置转储功能

**用户故事：** 作为 Envoy Gateway 管理员，我希望能够查看当前系统中的各种资源配置和状态信息。

#### 验收标准

1. When 用户点击 config_dump 菜单时，系统应当显示配置转储页面。
2. When 用户在配置页面时，系统应当通过 watchable 机制显示 Gateway、HTTPRoute、GatewayClass 等资源的配置信息。
3. When 用户点击具体资源时，系统应当显示该资源的详细配置和状态。
4. When 需要获取最新配置时，系统应当通过 Load 方法从 provider resource 获取数据。

### 需求 4 - Server Info 服务器信息功能

**用户故事：** 作为 Envoy Gateway 管理员，我希望能够查看系统的服务器信息和运行状态。

#### 验收标准

1. When 用户点击 server_info 菜单时，系统应当显示服务器信息页面。
2. When 用户在服务器信息页面时，系统应当显示 Envoy Gateway 各组件的运行状态。
3. When 用户在服务器信息页面时，系统应当显示系统的版本、启动时间等基本信息。
4. When 系统状态异常时，页面应当突出显示异常信息。

### 需求 5 - Stats 统计信息功能

**用户故事：** 作为 Envoy Gateway 管理员，我希望能够查看系统的统计信息和性能指标。

#### 验收标准

1. When 用户点击 stats 菜单时，系统应当显示统计信息页面。
2. When 用户在统计页面时，系统应当提供到 metrics 端点的直接链接。
3. When 用户点击 metrics 链接时，系统应当直接跳转到对应的 metrics port+path。
4. When 用户在统计页面时，系统应当显示关键性能指标的概览信息。

### 需求 6 - API 兼容性

**用户故事：** 作为开发者，我希望 Admin Console 的 API 设计与 Envoy Proxy Admin 保持一致，便于理解和使用。

#### 验收标准

1. When 设计 API 端点时，系统应当参考 Envoy Proxy Admin API 的命名规范。
2. When 实现功能时，系统应当保持与 Envoy Proxy Admin 类似的用户体验。
3. When 返回数据格式时，系统应当与 Envoy Proxy Admin 保持一致的数据结构。

### 需求 7 - 界面设计和扩展性

**用户故事：** 作为用户，我希望界面美观易用，作为开发者，我希望系统便于后续功能扩展。

#### 验收标准

1. When 用户访问任何页面时，界面应当美观、响应式，提供良好的用户体验。
2. When 开发新功能时，系统架构应当支持便捷的功能扩展。
3. When 用户在不同设备上访问时，界面应当适配不同的屏幕尺寸。
4. When 系统加载时，应当提供适当的加载提示和错误处理。

### 需求 8 - egctl Dashboard 集成

**用户故事：** 作为 Envoy Gateway 用户，我希望能够通过 egctl 命令快速启动和访问 Admin Console。

#### 验收标准

1. When 用户执行 `egctl x dashboard envoy-gateway` 时，系统应当自动设置端口转发到 admin server。
2. When 端口转发建立后，系统应当自动打开默认浏览器访问 console 主页。
3. When 用户指定命名空间参数时，系统应当连接到指定命名空间的 Envoy Gateway。
4. When 用户按 Ctrl+C 时，系统应当优雅地关闭端口转发和相关资源。
