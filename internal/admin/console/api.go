// Copyright Envoy Gateway Authors
// SPDX-License-Identifier: Apache-2.0
// The full text of the Apache license is available in the LICENSE file at
// the root of the repo.

package console

import (
	"encoding/json"
	"net/http"
	"runtime"
	"time"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	gwapiv1 "sigs.k8s.io/gateway-api/apis/v1"

	"github.com/envoyproxy/gateway/internal/cmd/version"
)

// SystemInfo represents basic system information
type SystemInfo struct {
	Version   string    `json:"version"`
	StartTime time.Time `json:"startTime"`
	Uptime    string    `json:"uptime"`
	GoVersion string    `json:"goVersion"`
	Platform  string    `json:"platform"`
	Timestamp time.Time `json:"timestamp"`
}

// ServerInfo represents server status information
type ServerInfo struct {
	State       string            `json:"state"`
	Version     string            `json:"version"`
	Uptime      string            `json:"uptime"`
	Components  []ComponentStatus `json:"components"`
	LastUpdated time.Time         `json:"lastUpdated"`
}

// ComponentStatus represents the status of a system component
type ComponentStatus struct {
	Name    string `json:"name"`
	Status  string `json:"status"`
	Message string `json:"message"`
}

// ConfigDumpInfo represents configuration dump information
type ConfigDumpInfo struct {
	Gateways     []ResourceSummary `json:"gateways"`
	HTTPRoutes   []ResourceSummary `json:"httpRoutes"`
	GatewayClass []ResourceSummary `json:"gatewayClass"`
	XdsIR        interface{}       `json:"xdsIR,omitempty"`
	InfraIR      interface{}       `json:"infraIR,omitempty"`
	LastUpdated  time.Time         `json:"lastUpdated"`
}

// ResourceSummary represents a summary of a Kubernetes resource
type ResourceSummary struct {
	Name      string `json:"name"`
	Namespace string `json:"namespace"`
	Status    string `json:"status"`
	Message   string `json:"message"`
}

var startTime = time.Now()

// handleAPIInfo returns basic system information
func (h *Handler) handleAPIInfo(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	uptime := time.Since(startTime)
	versionInfo := version.Get()
	info := SystemInfo{
		Version:   versionInfo.EnvoyGatewayVersion,
		StartTime: startTime,
		Uptime:    uptime.String(),
		GoVersion: runtime.Version(),
		Platform:  runtime.GOOS + "/" + runtime.GOARCH,
		Timestamp: time.Now(),
	}

	w.Header().Set("Content-Type", "application/json")
	if err := json.NewEncoder(w).Encode(info); err != nil {
		http.Error(w, "Failed to encode response", http.StatusInternalServerError)
		return
	}
}

// handleAPIServerInfo returns server status information
func (h *Handler) handleAPIServerInfo(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	uptime := time.Since(startTime)
	versionInfo := version.Get()

	// TODO: Implement actual component status checking
	components := []ComponentStatus{
		{
			Name:    "Admin Server",
			Status:  "Running",
			Message: "Admin server is operational",
		},
		{
			Name:    "XDS Server",
			Status:  "Running",
			Message: "XDS server is operational",
		},
		{
			Name:    "Gateway API Controller",
			Status:  "Running",
			Message: "Gateway API controller is operational",
		},
	}

	info := ServerInfo{
		State:       "Running",
		Version:     versionInfo.EnvoyGatewayVersion,
		Uptime:      uptime.String(),
		Components:  components,
		LastUpdated: time.Now(),
	}

	w.Header().Set("Content-Type", "application/json")
	if err := json.NewEncoder(w).Encode(info); err != nil {
		http.Error(w, "Failed to encode response", http.StatusInternalServerError)
		return
	}
}

// handleAPIConfigDump returns configuration dump information
func (h *Handler) handleAPIConfigDump(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// Load provider resources from watchable
	configDump := h.loadConfigDump()

	w.Header().Set("Content-Type", "application/json")
	if err := json.NewEncoder(w).Encode(configDump); err != nil {
		http.Error(w, "Failed to encode response", http.StatusInternalServerError)
		return
	}
}

// loadConfigDump loads configuration data from provider resources
func (h *Handler) loadConfigDump() ConfigDumpInfo {
	configDump := ConfigDumpInfo{
		Gateways:     []ResourceSummary{},
		HTTPRoutes:   []ResourceSummary{},
		GatewayClass: []ResourceSummary{},
		LastUpdated:  time.Now(),
	}

	if h.providerResources != nil {
		// Load all provider resources from the watchable map
		allProviderResources := h.providerResources.LoadAll()

		for _, providerRes := range allProviderResources {
			if providerRes == nil {
				continue
			}

			// Load controller resources from the provider resources
			controllerResources := providerRes.GatewayAPIResources.LoadAll()

			for _, resources := range controllerResources {
				if resources == nil {
					continue
				}

				for _, res := range *resources {
					if res == nil {
						continue
					}

					// Process GatewayClass
					if res.GatewayClass != nil {
						configDump.GatewayClass = append(configDump.GatewayClass, ResourceSummary{
							Name:      res.GatewayClass.Name,
							Namespace: "", // GatewayClass is cluster-scoped
							Status:    getGatewayClassStatus(res.GatewayClass),
							Message:   getGatewayClassMessage(res.GatewayClass),
						})
					}

					// Process Gateways
					for _, gateway := range res.Gateways {
						if gateway != nil {
							configDump.Gateways = append(configDump.Gateways, ResourceSummary{
								Name:      gateway.Name,
								Namespace: gateway.Namespace,
								Status:    getGatewayStatus(gateway),
								Message:   getGatewayMessage(gateway),
							})
						}
					}

					// Process HTTPRoutes
					for _, httpRoute := range res.HTTPRoutes {
						if httpRoute != nil {
							configDump.HTTPRoutes = append(configDump.HTTPRoutes, ResourceSummary{
								Name:      httpRoute.Name,
								Namespace: httpRoute.Namespace,
								Status:    getHTTPRouteStatus(httpRoute),
								Message:   getHTTPRouteMessage(httpRoute),
							})
						}
					}
				}
			}
		}
	}

	return configDump
}

// Helper functions to extract status information from Gateway API resources

func getGatewayClassStatus(gc *gwapiv1.GatewayClass) string {
	if len(gc.Status.Conditions) == 0 {
		return "Unknown"
	}

	for _, condition := range gc.Status.Conditions {
		if condition.Type == string(gwapiv1.GatewayClassConditionStatusAccepted) {
			if condition.Status == metav1.ConditionTrue {
				return "Accepted"
			}
			return "Not Accepted"
		}
	}
	return "Unknown"
}

func getGatewayClassMessage(gc *gwapiv1.GatewayClass) string {
	if len(gc.Status.Conditions) == 0 {
		return "No status information available"
	}

	for _, condition := range gc.Status.Conditions {
		if condition.Type == string(gwapiv1.GatewayClassConditionStatusAccepted) {
			return condition.Message
		}
	}
	return "No status information available"
}

func getGatewayStatus(gw *gwapiv1.Gateway) string {
	if len(gw.Status.Conditions) == 0 {
		return "Unknown"
	}

	// Check for Programmed condition first
	for _, condition := range gw.Status.Conditions {
		if condition.Type == string(gwapiv1.GatewayConditionProgrammed) {
			if condition.Status == metav1.ConditionTrue {
				return "Programmed"
			}
			return "Not Programmed"
		}
	}

	// Check for Accepted condition
	for _, condition := range gw.Status.Conditions {
		if condition.Type == string(gwapiv1.GatewayConditionAccepted) {
			if condition.Status == metav1.ConditionTrue {
				return "Accepted"
			}
			return "Not Accepted"
		}
	}

	return "Unknown"
}

func getGatewayMessage(gw *gwapiv1.Gateway) string {
	if len(gw.Status.Conditions) == 0 {
		return "No status information available"
	}

	// Get message from the most recent condition
	for _, condition := range gw.Status.Conditions {
		if condition.Type == string(gwapiv1.GatewayConditionProgrammed) ||
			condition.Type == string(gwapiv1.GatewayConditionAccepted) {
			return condition.Message
		}
	}

	return "No status information available"
}

func getHTTPRouteStatus(hr *gwapiv1.HTTPRoute) string {
	if len(hr.Status.Parents) == 0 {
		return "Unknown"
	}

	// Check the first parent's conditions
	parent := hr.Status.Parents[0]
	if len(parent.Conditions) == 0 {
		return "Unknown"
	}

	// Check for Accepted condition
	for _, condition := range parent.Conditions {
		if condition.Type == string(gwapiv1.RouteConditionAccepted) {
			if condition.Status == metav1.ConditionTrue {
				return "Accepted"
			}
			return "Not Accepted"
		}
	}

	return "Unknown"
}

func getHTTPRouteMessage(hr *gwapiv1.HTTPRoute) string {
	if len(hr.Status.Parents) == 0 {
		return "No status information available"
	}

	// Get message from the first parent's conditions
	parent := hr.Status.Parents[0]
	if len(parent.Conditions) == 0 {
		return "No status information available"
	}

	for _, condition := range parent.Conditions {
		if condition.Type == string(gwapiv1.RouteConditionAccepted) {
			return condition.Message
		}
	}

	return "No status information available"
}
