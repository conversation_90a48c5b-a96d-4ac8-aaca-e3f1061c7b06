// Envoy Gateway Admin Console JavaScript

// Global app object
window.EnvoyGatewayAdmin = {
    // Configuration
    config: {
        refreshInterval: 30000, // 30 seconds
        apiTimeout: 10000 // 10 seconds
    },
    
    // State
    state: {
        autoRefresh: false,
        currentPage: null
    },
    
    // Initialize the application
    init: function() {
        this.setupNavigation();
        this.setupAutoRefresh();
        this.detectCurrentPage();
        this.loadPageData();
    },
    
    // Setup navigation highlighting
    setupNavigation: function() {
        const currentPath = window.location.pathname;
        const navLinks = document.querySelectorAll('.nav a');
        
        navLinks.forEach(link => {
            if (link.getAttribute('href') === currentPath) {
                link.classList.add('active');
            }
        });
    },
    
    // Setup auto-refresh functionality
    setupAutoRefresh: function() {
        const refreshToggle = document.getElementById('auto-refresh');
        if (refreshToggle) {
            refreshToggle.addEventListener('change', (e) => {
                this.state.autoRefresh = e.target.checked;
                if (this.state.autoRefresh) {
                    this.startAutoRefresh();
                } else {
                    this.stopAutoRefresh();
                }
            });
        }
    },
    
    // Detect current page
    detectCurrentPage: function() {
        const path = window.location.pathname;
        if (path === '/' || path === '/index') {
            this.state.currentPage = 'index';
        } else if (path === '/server_info') {
            this.state.currentPage = 'server_info';
        } else if (path === '/config_dump') {
            this.state.currentPage = 'config_dump';
        } else if (path === '/stats') {
            this.state.currentPage = 'stats';
        } else if (path === '/pprof') {
            this.state.currentPage = 'pprof';
        }
    },
    
    // Load page-specific data
    loadPageData: function() {
        switch (this.state.currentPage) {
            case 'index':
                this.loadSystemInfo();
                break;
            case 'server_info':
                this.loadServerInfo();
                break;
            case 'config_dump':
                this.loadConfigDump();
                break;
        }
    },
    
    // API call helper
    apiCall: function(endpoint, callback) {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), this.config.apiTimeout);
        
        fetch(endpoint, {
            signal: controller.signal,
            headers: {
                'Accept': 'application/json'
            }
        })
        .then(response => {
            clearTimeout(timeoutId);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => callback(null, data))
        .catch(error => {
            clearTimeout(timeoutId);
            callback(error, null);
        });
    },
    
    // Load system information
    loadSystemInfo: function() {
        this.showLoading('system-info');
        this.apiCall('/api/info', (error, data) => {
            this.hideLoading('system-info');
            if (error) {
                this.showError('system-info', 'Failed to load system information: ' + error.message);
                return;
            }
            this.updateSystemInfo(data);
        });
    },
    
    // Update system information display
    updateSystemInfo: function(data) {
        const container = document.getElementById('system-info');
        if (!container) return;
        
        container.innerHTML = `
            <div class="info-box">
                <div>
                    <strong>Version:</strong> ${data.version}<br>
                    <strong>Uptime:</strong> ${data.uptime}<br>
                    <strong>Go Version:</strong> ${data.goVersion}<br>
                    <strong>Platform:</strong> ${data.platform}
                </div>
                <div>
                    <small>Last updated: ${new Date(data.timestamp).toLocaleString()}</small>
                </div>
            </div>
        `;
    },
    
    // Load server information
    loadServerInfo: function() {
        this.showLoading('server-info');
        this.apiCall('/api/server_info', (error, data) => {
            this.hideLoading('server-info');
            if (error) {
                this.showError('server-info', 'Failed to load server information: ' + error.message);
                return;
            }
            this.updateServerInfo(data);
        });
    },
    
    // Update server information display
    updateServerInfo: function(data) {
        const container = document.getElementById('server-info');
        if (!container) return;
        
        let componentsHtml = '';
        data.components.forEach(component => {
            const statusClass = component.status.toLowerCase() === 'running' ? 'running' : 'error';
            componentsHtml += `
                <tr>
                    <td>${component.name}</td>
                    <td><span class="status ${statusClass}">${component.status}</span></td>
                    <td>${component.message}</td>
                </tr>
            `;
        });
        
        container.innerHTML = `
            <div class="info-box">
                <div>
                    <strong>State:</strong> <span class="status running">${data.state}</span><br>
                    <strong>Version:</strong> ${data.version}<br>
                    <strong>Uptime:</strong> ${data.uptime}
                </div>
                <div>
                    <small>Last updated: ${new Date(data.lastUpdated).toLocaleString()}</small>
                </div>
            </div>
            <table class="table">
                <thead>
                    <tr>
                        <th>Component</th>
                        <th>Status</th>
                        <th>Message</th>
                    </tr>
                </thead>
                <tbody>
                    ${componentsHtml}
                </tbody>
            </table>
        `;
    },
    
    // Load configuration dump
    loadConfigDump: function() {
        this.showLoading('config-dump');
        this.apiCall('/api/config_dump', (error, data) => {
            this.hideLoading('config-dump');
            if (error) {
                this.showError('config-dump', 'Failed to load configuration dump: ' + error.message);
                return;
            }
            this.updateConfigDump(data);
        });
    },
    
    // Update configuration dump display
    updateConfigDump: function(data) {
        const container = document.getElementById('config-dump');
        if (!container) return;
        
        container.innerHTML = `
            <div class="info-box">
                <div>
                    <strong>Gateways:</strong> ${data.gateways.length}<br>
                    <strong>HTTP Routes:</strong> ${data.httpRoutes.length}<br>
                    <strong>Gateway Classes:</strong> ${data.gatewayClass.length}
                </div>
                <div>
                    <small>Last updated: ${new Date(data.lastUpdated).toLocaleString()}</small>
                </div>
            </div>
            <div class="code">
                ${JSON.stringify(data, null, 2)}
            </div>
        `;
    },
    
    // Show loading indicator
    showLoading: function(containerId) {
        const container = document.getElementById(containerId);
        if (container) {
            container.innerHTML = '<div class="loading"></div> Loading...';
        }
    },
    
    // Hide loading indicator
    hideLoading: function(containerId) {
        // Loading will be replaced by content
    },
    
    // Show error message
    showError: function(containerId, message) {
        const container = document.getElementById(containerId);
        if (container) {
            container.innerHTML = `<div class="info-box error">${message}</div>`;
        }
    },
    
    // Start auto-refresh
    startAutoRefresh: function() {
        if (this.refreshTimer) {
            clearInterval(this.refreshTimer);
        }
        this.refreshTimer = setInterval(() => {
            this.loadPageData();
        }, this.config.refreshInterval);
    },
    
    // Stop auto-refresh
    stopAutoRefresh: function() {
        if (this.refreshTimer) {
            clearInterval(this.refreshTimer);
            this.refreshTimer = null;
        }
    },
    
    // Manual refresh
    refresh: function() {
        this.loadPageData();
    }
};

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.EnvoyGatewayAdmin.init();
});

// Cleanup on page unload
window.addEventListener('beforeunload', function() {
    window.EnvoyGatewayAdmin.stopAutoRefresh();
});
