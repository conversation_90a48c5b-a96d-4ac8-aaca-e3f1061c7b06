{{template "base.html" .}}

{{define "stats-content"}}
<div class="card">
    <div class="card-header">
        <h1 class="card-title">{{.Title}}</h1>
    </div>
    <div class="card-body">
        <p>This page provides access to Envoy Gateway statistics and metrics. Metrics are exposed in Prometheus format for monitoring and alerting.</p>
        
        <div class="info-box">
            <div>
                <strong>Metrics Endpoint:</strong><br>
                <a href="/api/metrics" target="_blank">/api/metrics</a> (Available on Admin Port)
            </div>
            <div>
                <a href="/api/metrics" class="btn btn-primary" target="_blank">
                    📊 View Metrics
                </a>
            </div>
        </div>
    </div>
</div>

<div class="grid">
    <div class="card">
        <div class="card-header">
            <h2 class="card-title">📊 Control Plane Metrics</h2>
        </div>
        <div class="card-body">
            <p>Metrics related to the Envoy Gateway control plane components:</p>
            <ul>
                <li><strong>Gateway API Controller:</strong> Resource reconciliation metrics</li>
                <li><strong>XDS Server:</strong> Configuration distribution metrics</li>
                <li><strong>Admin Server:</strong> Administrative interface metrics</li>
                <li><strong>Infrastructure Manager:</strong> Infrastructure provisioning metrics</li>
            </ul>
            <a href="http://{{.MetricsAddress}}/metrics?filter=envoy_gateway" class="btn btn-secondary" target="_blank">
                View Control Plane Metrics
            </a>
        </div>
    </div>

    <div class="card">
        <div class="card-header">
            <h2 class="card-title">🔄 Runtime Metrics</h2>
        </div>
        <div class="card-body">
            <p>Go runtime and system metrics:</p>
            <ul>
                <li><strong>Memory Usage:</strong> Heap, stack, and GC metrics</li>
                <li><strong>Goroutines:</strong> Concurrency and scheduling metrics</li>
                <li><strong>HTTP Requests:</strong> Admin server request metrics</li>
                <li><strong>Process Info:</strong> CPU, memory, and file descriptor usage</li>
            </ul>
            <a href="http://{{.MetricsAddress}}/metrics?filter=go_" class="btn btn-secondary" target="_blank">
                View Runtime Metrics
            </a>
        </div>
    </div>

    <div class="card">
        <div class="card-header">
            <h2 class="card-title">🎯 Key Performance Indicators</h2>
        </div>
        <div class="card-body">
            <p>Important metrics to monitor for Envoy Gateway health:</p>
            <ul>
                <li><strong>Gateway Reconciliation Rate:</strong> How fast gateways are processed</li>
                <li><strong>XDS Configuration Push Rate:</strong> Configuration update frequency</li>
                <li><strong>Error Rates:</strong> Failed reconciliations and errors</li>
                <li><strong>Resource Counts:</strong> Number of managed resources</li>
            </ul>
        </div>
    </div>

    <div class="card">
        <div class="card-header">
            <h2 class="card-title">📈 Monitoring Integration</h2>
        </div>
        <div class="card-body">
            <p>Integrate with monitoring systems:</p>
            <ul>
                <li><strong>Prometheus:</strong> Scrape metrics for storage and alerting</li>
                <li><strong>Grafana:</strong> Create dashboards for visualization</li>
                <li><strong>AlertManager:</strong> Set up alerts for critical conditions</li>
                <li><strong>OpenTelemetry:</strong> Export metrics to OTEL collectors</li>
            </ul>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h2 class="card-title">🔧 Prometheus Configuration</h2>
    </div>
    <div class="card-body">
        <p>To scrape Envoy Gateway metrics with Prometheus, add this job to your <code>prometheus.yml</code>:</p>
        <div class="code">
- job_name: 'envoy-gateway'
  static_configs:
    - targets: ['{{.MetricsAddress}}']
  metrics_path: /metrics
  scrape_interval: 30s
  scrape_timeout: 10s
        </div>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h2 class="card-title">📊 Sample Grafana Queries</h2>
    </div>
    <div class="card-body">
        <p>Useful PromQL queries for monitoring Envoy Gateway:</p>
        <div class="code">
# Gateway reconciliation rate
rate(envoy_gateway_reconcile_total[5m])

# Memory usage
go_memstats_heap_inuse_bytes

# Goroutine count
go_goroutines

# HTTP request rate
rate(http_requests_total[5m])

# Error rate
rate(envoy_gateway_reconcile_errors_total[5m])
        </div>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h2 class="card-title">🚨 Recommended Alerts</h2>
    </div>
    <div class="card-body">
        <p>Set up these alerts for proactive monitoring:</p>
        <ul>
            <li><strong>High Error Rate:</strong> Alert when reconciliation errors exceed threshold</li>
            <li><strong>Memory Usage:</strong> Alert when memory usage is consistently high</li>
            <li><strong>Goroutine Leak:</strong> Alert when goroutine count grows continuously</li>
            <li><strong>XDS Push Failures:</strong> Alert when configuration pushes fail</li>
            <li><strong>Component Down:</strong> Alert when any component becomes unhealthy</li>
        </ul>
        
        <p>For more information about monitoring Envoy Gateway, see the <a href="https://gateway.envoyproxy.io/latest/tasks/observability/" target="_blank">observability documentation</a>.</p>
    </div>
</div>
{{end}}
