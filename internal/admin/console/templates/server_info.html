{{template "base.html" .}}

{{define "server-info-content"}}
<div class="card">
    <div class="card-header">
        <h1 class="card-title">{{.Title}}</h1>
        <div style="display: flex; align-items: center; gap: 1rem;">
            <label>
                <input type="checkbox" id="auto-refresh"> Auto-refresh (30s)
            </label>
            <button class="btn btn-secondary" onclick="EnvoyGatewayAdmin.refresh()">
                Refresh
            </button>
        </div>
    </div>
    <div class="card-body">
        <p>This page displays detailed information about the Envoy Gateway server status and components.</p>
        
        <div id="server-info">
            <div class="loading"></div> Loading server information...
        </div>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h2 class="card-title">📋 Component Health Check</h2>
    </div>
    <div class="card-body">
        <p>The table above shows the current status of all Envoy Gateway components. Since the Admin Server starts last in the startup sequence, if you can access this page, it means all components have started successfully and are healthy:</p>
        <ul>
            <li><span class="status running">Running</span> - Component is operational and healthy</li>
            <li><span class="status warning">Warning</span> - Component is running but may have issues</li>
            <li><span class="status error">Error</span> - Component is not functioning properly</li>
        </ul>
        <div class="info-box">
            <div>
                <strong>💡 Health Check Logic:</strong> The Admin Server is the last component to start. If this page loads successfully, all preceding components (Provider Service, Translators, XDS Server, etc.) are running correctly.
            </div>
        </div>
    </div>
</div>

<div class="grid">
    <div class="card">
        <div class="card-header">
            <h2 class="card-title">🔧 Admin Server</h2>
        </div>
        <div class="card-body">
            <p>The admin server provides this web interface and administrative endpoints for managing and monitoring Envoy Gateway.</p>
        </div>
    </div>

    <div class="card">
        <div class="card-header">
            <h2 class="card-title">🌐 XDS Server</h2>
        </div>
        <div class="card-body">
            <p>The XDS server manages the configuration distribution to Envoy proxies using the xDS protocol.</p>
        </div>
    </div>

    <div class="card">
        <div class="card-header">
            <h2 class="card-title">🎛️ Gateway API Controller</h2>
        </div>
        <div class="card-body">
            <p>The controller watches Kubernetes Gateway API resources and translates them into Envoy configuration.</p>
        </div>
    </div>

    <div class="card">
        <div class="card-header">
            <h2 class="card-title">📊 Metrics Server</h2>
        </div>
        <div class="card-body">
            <p>The metrics server exposes Prometheus metrics for monitoring Envoy Gateway performance and health.</p>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h2 class="card-title">🔍 Troubleshooting</h2>
    </div>
    <div class="card-body">
        <p>If any components show an error status, try the following troubleshooting steps:</p>
        <ol>
            <li>Check the Envoy Gateway logs for error messages</li>
            <li>Verify that all required Kubernetes resources are present</li>
            <li>Ensure proper RBAC permissions are configured</li>
            <li>Check network connectivity between components</li>
            <li>Verify resource limits and availability</li>
        </ol>
        
        <p>For more detailed troubleshooting information, visit the <a href="https://gateway.envoyproxy.io/latest/troubleshooting/" target="_blank">troubleshooting guide</a>.</p>
    </div>
</div>
{{end}}
