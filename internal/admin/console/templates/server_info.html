{{template "base.html" .}}

{{define "server-info-content"}}
<div class="card">
    <div class="card-header">
        <h1 class="card-title">{{.Title}}</h1>
        <div style="display: flex; align-items: center; gap: 1rem;">
            <label>
                <input type="checkbox" id="auto-refresh"> Auto-refresh (30s)
            </label>
            <button class="btn btn-secondary" onclick="EnvoyGatewayAdmin.refresh()">
                Refresh
            </button>
        </div>
    </div>
    <div class="card-body">
        <p>This page displays detailed information about the Envoy Gateway server status and components.</p>
        
        <div id="server-info">
            <div class="loading"></div> Loading server information...
        </div>
    </div>
</div>



<div class="grid">
    <div class="card">
        <div class="card-header">
            <h2 class="card-title">🔧 Admin Server</h2>
        </div>
        <div class="card-body">
            <p>The admin server provides this web interface and administrative endpoints for managing and monitoring Envoy Gateway.</p>
        </div>
    </div>

    <div class="card">
        <div class="card-header">
            <h2 class="card-title">🌐 XDS Server</h2>
        </div>
        <div class="card-body">
            <p>The XDS server manages the configuration distribution to Envoy proxies using the xDS protocol.</p>
        </div>
    </div>

    <div class="card">
        <div class="card-header">
            <h2 class="card-title">🎛️ Gateway API Controller</h2>
        </div>
        <div class="card-body">
            <p>The controller watches Kubernetes Gateway API resources and translates them into Envoy configuration.</p>
        </div>
    </div>

    <div class="card">
        <div class="card-header">
            <h2 class="card-title">📊 Metrics Server</h2>
        </div>
        <div class="card-body">
            <p>The metrics server exposes Prometheus metrics for monitoring Envoy Gateway performance and health.</p>
        </div>
    </div>
</div>


{{end}}
