{{template "base.html" .}}

{{define "config-dump-content"}}
<div class="card">
    <div class="card-header">
        <h1 class="card-title">{{.Title}}</h1>
        <div style="display: flex; align-items: center; gap: 1rem;">
            <a href="/api/config_dump_all" class="btn btn-primary" target="_blank">
                📥 Download Complete Config (JSON)
            </a>
            <label>
                <input type="checkbox" id="auto-refresh"> Auto-refresh (30s)
            </label>
            <button class="btn btn-secondary" onclick="EnvoyGatewayAdmin.refresh()">
                Refresh
            </button>
        </div>
    </div>
    <div class="card-body">
        <p>This page displays the current configuration state of Envoy Gateway, including all Gateway API resources and their status.</p>
        
        <div id="config-dump">
            <div class="loading"></div> Loading configuration dump...
        </div>
    </div>
</div>

<div class="grid">
    <div class="card">
        <div class="card-header">
            <h2 class="card-title">🚪 Gateways</h2>
        </div>
        <div class="card-body">
            <p>Gateway resources define the entry points for traffic into the cluster. They specify which ports and protocols are exposed.</p>
            <div id="gateways-list">
                <div class="loading"></div> Loading gateways...
            </div>
        </div>
    </div>

    <div class="card">
        <div class="card-header">
            <h2 class="card-title">🛣️ HTTP Routes</h2>
        </div>
        <div class="card-body">
            <p>HTTPRoute resources define how HTTP requests are routed to backend services.</p>
            <div id="httproutes-list">
                <div class="loading"></div> Loading HTTP routes...
            </div>
        </div>
    </div>

    <div class="card">
        <div class="card-header">
            <h2 class="card-title">🔧 GRPC Routes</h2>
        </div>
        <div class="card-body">
            <p>GRPCRoute resources define how gRPC requests are routed to backend services.</p>
            <div id="grpcroutes-list">
                <div class="loading"></div> Loading GRPC routes...
            </div>
        </div>
    </div>

    <div class="card">
        <div class="card-header">
            <h2 class="card-title">🔒 TLS Routes</h2>
        </div>
        <div class="card-body">
            <p>TLSRoute resources define how TLS connections are routed to backend services.</p>
            <div id="tlsroutes-list">
                <div class="loading"></div> Loading TLS routes...
            </div>
        </div>
    </div>

    <div class="card">
        <div class="card-header">
            <h2 class="card-title">🔌 TCP Routes</h2>
        </div>
        <div class="card-body">
            <p>TCPRoute resources define how TCP connections are routed to backend services.</p>
            <div id="tcproutes-list">
                <div class="loading"></div> Loading TCP routes...
            </div>
        </div>
    </div>

    <div class="card">
        <div class="card-header">
            <h2 class="card-title">📡 UDP Routes</h2>
        </div>
        <div class="card-body">
            <p>UDPRoute resources define how UDP traffic is routed to backend services.</p>
            <div id="udproutes-list">
                <div class="loading"></div> Loading UDP routes...
            </div>
        </div>
    </div>

    <div class="card">
        <div class="card-header">
            <h2 class="card-title">🏷️ Gateway Classes</h2>
        </div>
        <div class="card-body">
            <p>GatewayClass resources define the type of Gateway that can be deployed.</p>
            <div id="gatewayclasses-list">
                <div class="loading"></div> Loading gateway classes...
            </div>
        </div>
    </div>


</div>

<div class="card">
    <div class="card-header">
        <h2 class="card-title">📊 Configuration Summary</h2>
    </div>
    <div class="card-body">
        <p>This section provides a high-level overview of the current configuration state:</p>
        <div id="config-summary">
            <div class="loading"></div> Loading configuration summary...
        </div>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h2 class="card-title">💡 Understanding Configuration</h2>
    </div>
    <div class="card-body">
        <h3>Resource Relationships</h3>
        <ul>
            <li><strong>GatewayClass:</strong> Defines the controller and configuration template</li>
            <li><strong>Gateway:</strong> References a GatewayClass and defines listeners</li>
            <li><strong>HTTPRoute:</strong> References a Gateway and defines routing rules</li>
        </ul>
        
        <h3>Status Meanings</h3>
        <ul>
            <li><span class="status running">Accepted</span> - Resource is valid and accepted</li>
            <li><span class="status running">Programmed</span> - Resource is successfully configured</li>
            <li><span class="status warning">Pending</span> - Resource is being processed</li>
            <li><span class="status error">Invalid</span> - Resource has configuration errors</li>
        </ul>
        
        <p>For more information about Gateway API resources, see the <a href="https://gateway-api.sigs.k8s.io/" target="_blank">Gateway API documentation</a>.</p>
    </div>
</div>

<script>

</script>
{{end}}
