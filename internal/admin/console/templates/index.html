{{template "base.html" .}}

{{define "index-content"}}
<div class="card">
    <div class="card-header">
        <h1 class="card-title">{{.Title}}</h1>
        <div style="display: flex; align-items: center; gap: 1rem;">
            <label>
                <input type="checkbox" id="auto-refresh"> Auto-refresh (30s)
            </label>
            <button class="btn btn-secondary" onclick="EnvoyGatewayAdmin.refresh()">
                Refresh
            </button>
        </div>
    </div>
    <div class="card-body">
        <p>Welcome to the Envoy Gateway Admin Console. This interface provides access to various administrative functions and monitoring capabilities.</p>
        
        <div id="system-info">
            <div class="loading"></div> Loading system information...
        </div>
    </div>
</div>

<div class="grid">
    <div class="card">
        <div class="card-header">
            <h2 class="card-title">🖥️ Server Information</h2>
        </div>
        <div class="card-body">
            <p>View detailed information about the Envoy Gateway server status, components, and runtime information.</p>
            <a href="/server_info" class="btn">View Server Info</a>
        </div>
    </div>

    <div class="card">
        <div class="card-header">
            <h2 class="card-title">⚙️ Configuration Dump</h2>
        </div>
        <div class="card-body">
            <p>Access the current configuration state including Gateways, HTTPRoutes, and other resources.</p>
            <a href="/config_dump" class="btn">View Config Dump</a>
        </div>
    </div>

    <div class="card">
        <div class="card-header">
            <h2 class="card-title">📊 Statistics</h2>
        </div>
        <div class="card-body">
            <p>Monitor performance metrics and statistics from the Envoy Gateway control plane.</p>
            <a href="/stats" class="btn">View Statistics</a>
        </div>
    </div>

    <div class="card">
        <div class="card-header">
            <h2 class="card-title">🔍 Performance Profiling</h2>
        </div>
        <div class="card-body">
            <p>Access pprof endpoints for performance analysis and debugging.</p>
            <a href="/pprof" class="btn">View pprof</a>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h2 class="card-title">📚 Quick Links</h2>
    </div>
    <div class="card-body">
        <div class="grid">
            <div>
                <h3>Documentation</h3>
                <ul>
                    <li><a href="https://gateway.envoyproxy.io/" target="_blank">Envoy Gateway Docs</a></li>
                    <li><a href="https://gateway.envoyproxy.io/latest/api/" target="_blank">API Reference</a></li>
                    <li><a href="https://gateway.envoyproxy.io/latest/tasks/" target="_blank">Tasks & Tutorials</a></li>
                </ul>
            </div>
            <div>
                <h3>Community</h3>
                <ul>
                    <li><a href="https://github.com/envoyproxy/gateway" target="_blank">GitHub Repository</a></li>
                    <li><a href="https://github.com/envoyproxy/gateway/issues" target="_blank">Issue Tracker</a></li>
                    <li><a href="https://envoyproxy.slack.com/" target="_blank">Slack Community</a></li>
                </ul>
            </div>
        </div>
    </div>
</div>
{{end}}
