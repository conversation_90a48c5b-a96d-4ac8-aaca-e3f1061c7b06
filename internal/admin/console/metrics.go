// Copyright Envoy Gateway Authors
// SPDX-License-Identifier: Apache-2.0
// The full text of the Apache license is available in the LICENSE file at
// the root of the repo.

package console

import (
	"net/http"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	metricsserver "sigs.k8s.io/controller-runtime/pkg/metrics"
)

// createMetricsHandler creates a Prometheus metrics handler using the controller-runtime registry
func createMetricsHandler() http.Handler {
	// Use the controller-runtime metrics registry
	// This is where Envoy Gateway registers its control plane metrics
	return promhttp.HandlerFor(metricsserver.Registry, promhttp.HandlerOpts{})
}

// createCombinedMetricsHandler creates a handler that combines multiple registries
func createCombinedMetricsHandler() http.Handler {
	// Create a gatherer that combines the controller-runtime registry with other registries
	// This mimics what the metrics server does in internal/metrics/register.go
	gatherers := prometheus.Gatherers{
		metricsserver.Registry,    // Controller-runtime registry (main Envoy Gateway metrics)
		prometheus.DefaultGatherer, // Default registry (Go runtime metrics, etc.)
	}
	
	return promhttp.HandlerFor(gatherers, promhttp.HandlerOpts{})
}
