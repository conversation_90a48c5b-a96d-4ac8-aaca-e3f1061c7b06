// Copyright Envoy Gateway Authors
// SPDX-License-Identifier: Apache-2.0
// The full text of the Apache license is available in the LICENSE file at
// the root of the repo.

package console

import (
	"fmt"
	"html/template"
	"io/fs"
	"net/http"
	"time"

	egv1a1 "github.com/envoyproxy/gateway/api/v1alpha1"
	"github.com/envoyproxy/gateway/internal/envoygateway/config"
	"github.com/envoyproxy/gateway/internal/message"
)

// <PERSON><PERSON> manages the console web interface
type Handler struct {
	cfg               *config.Server
	templates         map[string]*template.Template
	providerResources *message.ProviderResources
}

// NewHandler creates a new console handler
func NewHandler(cfg *config.Server, providerResources *message.ProviderResources) *Handler {
	return &Handler{
		cfg:               cfg,
		templates:         make(map[string]*template.Template),
		providerResources: providerResources,
	}
}

// RegisterRoutes registers all console routes with the provided mux
func (h *Handler) RegisterRoutes(mux *http.ServeMux) {
	// Web UI endpoints
	mux.HandleFunc("/", h.handleIndex)
	mux.HandleFunc("/pprof", h.handlePprof)
	mux.HandleFunc("/server_info", h.handleServerInfo)
	mux.HandleFunc("/config_dump", h.handleConfigDump)
	mux.HandleFunc("/stats", h.handleStats)

	// API endpoints
	mux.HandleFunc("/api/info", h.handleAPIInfo)
	mux.HandleFunc("/api/server_info", h.handleAPIServerInfo)
	mux.HandleFunc("/api/config_dump", h.handleAPIConfigDump)

	// Static files
	staticFS, err := fs.Sub(staticFiles, "static")
	if err == nil {
		mux.Handle("/static/", http.StripPrefix("/static/", http.FileServer(http.FS(staticFS))))
	}
}

// handleIndex serves the main console page
func (h *Handler) handleIndex(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	data := struct {
		Title     string
		Timestamp time.Time
	}{
		Title:     "Envoy Gateway Admin Console",
		Timestamp: time.Now(),
	}

	if err := h.renderTemplate(w, "index.html", data); err != nil {
		// Content-Type is already set by renderTemplate
		w.WriteHeader(http.StatusInternalServerError)
		w.Write([]byte("Internal server error"))
		return
	}
}

// handlePprof serves the pprof page
func (h *Handler) handlePprof(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	enablePprof := h.cfg.EnvoyGateway.GetEnvoyGatewayAdmin().EnablePprof
	data := struct {
		Title       string
		EnablePprof bool
	}{
		Title:       "Performance Profiling",
		EnablePprof: enablePprof,
	}

	if err := h.renderTemplate(w, "pprof.html", data); err != nil {
		// Content-Type is already set by renderTemplate
		w.WriteHeader(http.StatusInternalServerError)
		w.Write([]byte("Internal server error"))
		return
	}
}

// handleServerInfo serves the server info page
func (h *Handler) handleServerInfo(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	data := struct {
		Title string
	}{
		Title: "Server Information",
	}

	if err := h.renderTemplate(w, "server_info.html", data); err != nil {
		// Content-Type is already set by renderTemplate
		w.WriteHeader(http.StatusInternalServerError)
		w.Write([]byte("Internal server error"))
		return
	}
}

// handleConfigDump serves the config dump page
func (h *Handler) handleConfigDump(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	data := struct {
		Title string
	}{
		Title: "Configuration Dump",
	}

	if err := h.renderTemplate(w, "config_dump.html", data); err != nil {
		// Content-Type is already set by renderTemplate
		w.WriteHeader(http.StatusInternalServerError)
		w.Write([]byte("Internal server error"))
		return
	}
}

// handleStats serves the stats page
func (h *Handler) handleStats(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// Get metrics server address
	metricsAddress := fmt.Sprintf("%s:%d", egv1a1.GatewayMetricsHost, egv1a1.GatewayMetricsPort)

	data := struct {
		Title          string
		MetricsAddress string
	}{
		Title:          "Statistics",
		MetricsAddress: metricsAddress,
	}

	if err := h.renderTemplate(w, "stats.html", data); err != nil {
		// Content-Type is already set by renderTemplate
		w.WriteHeader(http.StatusInternalServerError)
		w.Write([]byte("Internal server error"))
		return
	}
}

// renderTemplate renders a template with the given data
func (h *Handler) renderTemplate(w http.ResponseWriter, tmpl string, data interface{}) error {
	if len(h.templates) == 0 {
		if err := h.loadTemplates(); err != nil {
			// Set Content-Type before returning error
			w.Header().Set("Content-Type", "text/html; charset=utf-8")
			return err
		}
	}

	w.Header().Set("Content-Type", "text/html; charset=utf-8")

	// Get the specific template
	t, exists := h.templates[tmpl]
	if !exists {
		return fmt.Errorf("template %s not found", tmpl)
	}

	return t.Execute(w, data)
}

// loadTemplates loads all HTML templates
func (h *Handler) loadTemplates() error {
	templateNames := []string{
		"index.html",
		"server_info.html",
		"config_dump.html",
		"stats.html",
		"pprof.html",
	}

	// Create individual templates for each page
	for _, templateName := range templateNames {
		// Create a new template instance for each page
		tmpl := template.New(templateName)

		// Parse base template first
		tmpl, err := tmpl.ParseFS(templateFiles, "templates/base.html")
		if err != nil {
			return fmt.Errorf("failed to parse base template for %s: %w", templateName, err)
		}

		// Parse the specific page template
		tmpl, err = tmpl.ParseFS(templateFiles, "templates/"+templateName)
		if err != nil {
			return fmt.Errorf("failed to parse template %s: %w", templateName, err)
		}

		h.templates[templateName] = tmpl
	}

	return nil
}
