// Copyright Envoy Gateway Authors
// SPDX-License-Identifier: Apache-2.0
// The full text of the Apache license is available in the LICENSE file at
// the root of the repo.

package console

import (
	"fmt"
	"html/template"
	"net/http"
	"time"

	"github.com/telepresenceio/watchable"

	egv1a1 "github.com/envoyproxy/gateway/api/v1alpha1"
	"github.com/envoyproxy/gateway/internal/envoygateway/config"
	"github.com/envoyproxy/gateway/internal/message"
)

// <PERSON><PERSON> manages the console web interface
type Handler struct {
	cfg               *config.Server
	templates         *template.Template
	providerResources watchable.Map[string, *message.ProviderResources]
}

// NewHandler creates a new console handler
func <PERSON>Handler(cfg *config.Server, providerResources watchable.Map[string, *message.ProviderResources]) *Handler {
	return &Handler{
		cfg:               cfg,
		providerResources: providerResources,
	}
}

// RegisterRoutes registers all console routes with the provided mux
func (h *Handler) RegisterRoutes(mux *http.ServeMux) {
	// Web UI endpoints
	mux.HandleFunc("/", h.handleIndex)
	mux.HandleFunc("/pprof", h.handlePprof)
	mux.HandleFunc("/server_info", h.handleServerInfo)
	mux.HandleFunc("/config_dump", h.handleConfigDump)
	mux.HandleFunc("/stats", h.handleStats)

	// API endpoints
	mux.HandleFunc("/api/info", h.handleAPIInfo)
	mux.HandleFunc("/api/server_info", h.handleAPIServerInfo)
	mux.HandleFunc("/api/config_dump", h.handleAPIConfigDump)

	// Static files
	mux.Handle("/static/", http.StripPrefix("/static/", http.FileServer(http.FS(staticFiles))))
}

// handleIndex serves the main console page
func (h *Handler) handleIndex(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	data := struct {
		Title     string
		Timestamp time.Time
	}{
		Title:     "Envoy Gateway Admin Console",
		Timestamp: time.Now(),
	}

	if err := h.renderTemplate(w, "index.html", data); err != nil {
		http.Error(w, "Internal server error", http.StatusInternalServerError)
		return
	}
}

// handlePprof serves the pprof page
func (h *Handler) handlePprof(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	enablePprof := h.cfg.EnvoyGateway.GetEnvoyGatewayAdmin().EnablePprof
	data := struct {
		Title       string
		EnablePprof bool
	}{
		Title:       "Performance Profiling",
		EnablePprof: enablePprof,
	}

	if err := h.renderTemplate(w, "pprof.html", data); err != nil {
		http.Error(w, "Internal server error", http.StatusInternalServerError)
		return
	}
}

// handleServerInfo serves the server info page
func (h *Handler) handleServerInfo(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	data := struct {
		Title string
	}{
		Title: "Server Information",
	}

	if err := h.renderTemplate(w, "server_info.html", data); err != nil {
		http.Error(w, "Internal server error", http.StatusInternalServerError)
		return
	}
}

// handleConfigDump serves the config dump page
func (h *Handler) handleConfigDump(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	data := struct {
		Title string
	}{
		Title: "Configuration Dump",
	}

	if err := h.renderTemplate(w, "config_dump.html", data); err != nil {
		http.Error(w, "Internal server error", http.StatusInternalServerError)
		return
	}
}

// handleStats serves the stats page
func (h *Handler) handleStats(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// Get metrics server address
	metricsAddress := fmt.Sprintf("%s:%d", egv1a1.GatewayMetricsHost, egv1a1.GatewayMetricsPort)

	data := struct {
		Title          string
		MetricsAddress string
	}{
		Title:          "Statistics",
		MetricsAddress: metricsAddress,
	}

	if err := h.renderTemplate(w, "stats.html", data); err != nil {
		http.Error(w, "Internal server error", http.StatusInternalServerError)
		return
	}
}

// renderTemplate renders a template with the given data
func (h *Handler) renderTemplate(w http.ResponseWriter, tmpl string, data interface{}) error {
	if h.templates == nil {
		return h.loadTemplates()
	}

	w.Header().Set("Content-Type", "text/html; charset=utf-8")
	return h.templates.ExecuteTemplate(w, tmpl, data)
}

// loadTemplates loads all HTML templates
func (h *Handler) loadTemplates() error {
	var err error
	h.templates, err = template.ParseFS(templateFiles, "templates/*.html")
	return err
}
