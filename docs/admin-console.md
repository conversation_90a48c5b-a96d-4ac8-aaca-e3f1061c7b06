# Envoy Gateway Admin Console

The Envoy Gateway Admin Console provides a web-based interface for monitoring and managing your Envoy Gateway deployment. It offers real-time insights into system status, configuration, and performance metrics.

## Features

- **Dashboard**: Overview of system status and key metrics
- **Server Information**: Detailed information about Envoy Gateway components and their health
- **Configuration Dump**: View current Gateway API resources and their status
- **Statistics**: Access to Prometheus metrics and performance data
- **Performance Profiling**: pprof endpoints for debugging and performance analysis

## Enabling the Admin Console

The Admin Console is enabled by default in Envoy Gateway. You can control its availability through the EnvoyGateway configuration:

```yaml
apiVersion: gateway.envoyproxy.io/v1alpha1
kind: EnvoyGateway
metadata:
  name: envoy-gateway-config
  namespace: envoy-gateway-system
spec:
  admin:
    enableConsole: true  # Enable the admin console (default: true)
    enablePprof: true    # Enable pprof endpoints (default: false)
    address:
      host: "127.0.0.1"  # Admin server host (default: 127.0.0.1)
      port: 19000        # Admin server port (default: 19000)
```

## Accessing the Admin Console

### Method 1: Using egctl (Recommended)

The easiest way to access the admin console is using the `egctl` command:

```bash
# Open the admin console in your default browser
egctl x dashboard envoy-gateway

# Specify a custom namespace
egctl x dashboard envoy-gateway -n custom-namespace

# Use a specific local port
egctl x dashboard envoy-gateway --port 8080
```

This command will:
1. Set up port forwarding to the Envoy Gateway admin server
2. Automatically open your default browser to the console
3. Display the console URL for manual access

### Method 2: Manual Port Forwarding

You can also manually set up port forwarding:

```bash
# Forward local port 19000 to the admin server
kubectl port-forward -n envoy-gateway-system deployment/envoy-gateway 19000:19000

# Access the console in your browser
open http://localhost:19000
```

## Console Pages

### Dashboard (/)

The main dashboard provides:
- System information (version, uptime, platform)
- Quick navigation to other console sections
- Links to documentation and community resources

### Server Information (/server_info)

Displays detailed information about:
- Envoy Gateway version and uptime
- Component health status (Admin Server, XDS Server, Gateway API Controller)
- System state and operational status

### Configuration Dump (/config_dump)

Shows the current state of Gateway API resources:
- Gateway Classes and their acceptance status
- Gateways and their programming status
- HTTP Routes and their acceptance status
- Resource counts and summaries

### Statistics (/stats)

Provides access to:
- Direct links to Prometheus metrics endpoint
- Control plane metrics overview
- Runtime metrics information
- Monitoring integration guidance

### Performance Profiling (/pprof)

When enabled, offers access to:
- CPU profiling
- Memory heap analysis
- Goroutine inspection
- Mutex and blocking analysis

## API Endpoints

The console also exposes JSON API endpoints for programmatic access:

- `GET /api/info` - System information
- `GET /api/server_info` - Server status and components
- `GET /api/config_dump` - Configuration dump data

Example API usage:

```bash
# Get system information
curl http://localhost:19000/api/info

# Get server status
curl http://localhost:19000/api/server_info

# Get configuration dump
curl http://localhost:19000/api/config_dump
```

## Configuration Examples

### Enable Console with Custom Settings

```yaml
apiVersion: gateway.envoyproxy.io/v1alpha1
kind: EnvoyGateway
metadata:
  name: envoy-gateway-config
spec:
  admin:
    enableConsole: true
    enablePprof: true
    enableDumpConfig: true
    address:
      host: "0.0.0.0"  # Allow external access (use with caution)
      port: 19000
```

### Disable Console for Production

```yaml
apiVersion: gateway.envoyproxy.io/v1alpha1
kind: EnvoyGateway
metadata:
  name: envoy-gateway-config
spec:
  admin:
    enableConsole: false  # Disable console for security
    enablePprof: false    # Disable pprof for security
    address:
      host: "127.0.0.1"   # Localhost only
      port: 19000
```

## Security Considerations

- **Network Access**: By default, the admin server binds to `127.0.0.1`, making it accessible only from within the pod
- **Production Deployments**: Consider disabling the console in production environments
- **pprof Endpoints**: Only enable pprof when needed for debugging, as it can expose sensitive information
- **Authentication**: The console does not include built-in authentication; use network policies or service mesh for access control

## Troubleshooting

### Console Not Accessible

1. Verify the console is enabled:
   ```bash
   kubectl get envoygateway -n envoy-gateway-system -o yaml
   ```

2. Check the admin server logs:
   ```bash
   kubectl logs -n envoy-gateway-system deployment/envoy-gateway
   ```

3. Verify port forwarding:
   ```bash
   kubectl port-forward -n envoy-gateway-system deployment/envoy-gateway 19000:19000
   ```

### Empty Configuration Dump

If the configuration dump shows no resources:
1. Verify Gateway API resources are created and accepted
2. Check the Envoy Gateway controller logs for errors
3. Ensure proper RBAC permissions for the controller

### Performance Issues

If the console is slow to load:
1. Check network connectivity to the admin server
2. Verify the Envoy Gateway pod has sufficient resources
3. Monitor the admin server metrics for performance bottlenecks

## Integration with Monitoring

The admin console complements your monitoring setup:

### Prometheus Integration

```yaml
# Prometheus scrape configuration
- job_name: 'envoy-gateway'
  static_configs:
    - targets: ['envoy-gateway.envoy-gateway-system.svc.cluster.local:19001']
  metrics_path: /metrics
  scrape_interval: 30s
```

### Grafana Dashboards

Use the console's `/stats` page to find relevant metrics for your Grafana dashboards:
- `envoy_gateway_reconcile_total` - Reconciliation rate
- `go_memstats_heap_inuse_bytes` - Memory usage
- `go_goroutines` - Goroutine count

## Related Documentation

- [Envoy Gateway Configuration](https://gateway.envoyproxy.io/latest/api/config_types/)
- [Observability Guide](https://gateway.envoyproxy.io/latest/tasks/observability/)
- [Troubleshooting](https://gateway.envoyproxy.io/latest/troubleshooting/)
